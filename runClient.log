
> Configure project :
Fabric Loom: 1.10.5

> Task :compileJava
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/town/client/ClaimToolDataSynchronizer.java:80: warning: [unchecked] unchecked cast
                Map<String, Object> eventData = (Map<String, Object>) data;
                                                                      ^
  required: Map<String,Object>
  found:    Object
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/config/ConfigSynchronizer.java:162: warning: [unchecked] unchecked cast
            return (Map<String, Object>) categoryPrefs;
                                         ^
  required: Map<String,Object>
  found:    Object
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/config/ConfigSynchronizer.java:210: warning: [unchecked] unchecked conversion
            Map<String, Object> preferences = gson.fromJson(jsonObject, Map.class);
                                                           ^
  required: Map<String,Object>
  found:    Map
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/config/ConfigSynchronizer.java:361: warning: [unchecked] unchecked conversion
            Map<String, Object> config = gson.fromJson(jsonValue, Map.class);
                                                      ^
  required: Map<String,Object>
  found:    Map
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/config/UserPreferencesManager.java:60: warning: [unchecked] unchecked conversion
            Map<String, Map<String, Object>> loaded = gson.fromJson(reader, Map.class);
                                                                   ^
  required: Map<String,Map<String,Object>>
  found:    Map
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/config/UserPreferencesManager.java:189: warning: [unchecked] unchecked cast
            return (T) value;
                       ^
  required: T
  found:    Object
  where T is a type-variable:
    T extends Object declared in method <T>getPreference(String,String,T)
6 warnings

> Task :processResources UP-TO-DATE
> Task :classes
> Task :jar UP-TO-DATE
> Task :compileTestJava NO-SOURCE
> Task :processIncludeJars UP-TO-DATE
> Task :sourcesJar UP-TO-DATE
> Task :processTestResources NO-SOURCE
> Task :testClasses UP-TO-DATE
> Task :test NO-SOURCE
> Task :validateAccessWidener NO-SOURCE
> Task :check UP-TO-DATE
> Task :cleanRun UP-TO-DATE
> Task :generateLog4jConfig UP-TO-DATE
> Task :generateRemapClasspath UP-TO-DATE
> Task :generateDLIConfig UP-TO-DATE
> Task :configureLaunch UP-TO-DATE
> Task :downloadAssets UP-TO-DATE
> Task :configureClientLaunch UP-TO-DATE
> Task :remapJar
> Task :remapSourcesJar
> Task :assemble
> Task :build

> Task :runClient
[34m[05:10:27][m [32m[main/INFO][m [36m(FabricLoader/GameProvider)[m [0mLoading Minecraft 1.20.1 with Fabric Loader 0.16.13
[m[34m[05:10:27][m [32m[main/INFO][m [36m(FabricLoader)[m [0mLoading 59 mods:
	- fabric-api 0.92.5*****.1
	- fabric-api-base 0.4.32+1802ada577
	- fabric-api-lookup-api-v1 1.6.37+1802ada577
	- fabric-biome-api-v1 13.0.14+1802ada577
	- fabric-block-api-v1 1.0.12+1802ada577
	- fabric-block-view-api-v2 1.0.3+924f046a77
	- fabric-blockrenderlayer-v1 1.1.42+1802ada577
	- fabric-client-tags-api-v1 1.1.3+1802ada577
	- fabric-command-api-v1 1.2.35+f71b366f77
	- fabric-command-api-v2 2.2.14+1802ada577
	- fabric-commands-v0 0.2.52+df3654b377
	- fabric-containers-v0 0.1.66+df3654b377
	- fabric-content-registries-v0 4.0.13+1802ada577
	- fabric-convention-tags-v1 1.5.6+1802ada577
	- fabric-crash-report-info-v1 0.2.20+1802ada577
	- fabric-data-attachment-api-v1 1.0.2+de0fd6d177
	- fabric-data-generation-api-v1 12.3.6+1802ada577
	- fabric-dimensions-v1 2.1.55+1802ada577
	- fabric-entity-events-v1 1.6.1+1c78457f77
	- fabric-events-interaction-v0 0.6.4+13a40c6677
	- fabric-events-lifecycle-v0 0.2.64+df3654b377
	- fabric-game-rule-api-v1 1.0.41+1802ada577
	- fabric-gametest-api-v1 1.2.15+1802ada577
	- fabric-item-api-v1 2.1.29+1802ada577
	- fabric-item-group-api-v1 4.0.14+1802ada577
	- fabric-key-binding-api-v1 1.0.38+1802ada577
	- fabric-keybindings-v0 0.2.36+df3654b377
	- fabric-lifecycle-events-v1 2.2.23+1802ada577
	- fabric-loot-api-v2 1.2.3+1802ada577
	- fabric-loot-tables-v1 1.1.47+9e7660c677
	- fabric-message-api-v1 5.1.10+1802ada577
	- fabric-mining-level-api-v1 2.1.52+1802ada577
	- fabric-model-loading-api-v1 1.0.4+1802ada577
	- fabric-models-v0 0.4.3+9386d8a777
	- fabric-networking-api-v1 1.3.13+13a40c6677
	- fabric-networking-v0 0.3.53+df3654b377
	- fabric-object-builder-api-v1 11.1.5+e35120df77
	- fabric-particles-v1 1.1.3+1802ada577
	- fabric-recipe-api-v1 1.0.23+1802ada577
	- fabric-registry-sync-v0 2.3.5+1802ada577
	- fabric-renderer-api-v1 3.2.2+1802ada577
	- fabric-renderer-indigo 1.5.3+85287f9f77
	- fabric-renderer-registries-v1 3.2.47+df3654b377
	- fabric-rendering-data-attachment-v1 0.3.39+92a0d36777
	- fabric-rendering-fluids-v1 3.0.29+1802ada577
	- fabric-rendering-v0 1.1.50+df3654b377
	- fabric-rendering-v1 3.0.9+1802ada577
	- fabric-resource-conditions-api-v1 2.3.9+1802ada577
	- fabric-resource-loader-v0 0.11.12+fb82e9d777
	- fabric-screen-api-v1 2.0.9+1802ada577
	- fabric-screen-handler-api-v1 1.3.32+1802ada577
	- fabric-sound-api-v1 1.0.14+1802ada577
	- fabric-transfer-api-v1 3.3.6+8dd72ea377
	- fabric-transitive-access-wideners-v1 4.3.2+1802ada577
	- fabricloader 0.16.13
	- java 21
	- minecraft 1.20.1
	- mixinextras 0.4.1
	- pokecobbleclaim 1.0.0
[m[34m[05:10:27][m [32m[main/INFO][m [36m(FabricLoader/Mixin)[m [0mSpongePowered MIXIN Subsystem Version=0.8.7 Source=file:/home/<USER>/.gradle/caches/modules-2/files-2.1/net.fabricmc/sponge-mixin/0.15.4+mixin.0.8.7/6a12aacc794f1078458433116e9ed42c1cc98096/sponge-mixin-0.15.4+mixin.0.8.7.jar Service=Knot/Fabric Env=CLIENT
[m[34m[05:10:27][m [32m[main/INFO][m [36m(FabricLoader/Mixin)[m [0mLoaded Fabric development mappings for mixin remapper!
[m[34m[05:10:27][m [32m[main/INFO][m [36m(FabricLoader/Mixin)[m [0mCompatibility level set to JAVA_17
[m[34m[05:10:30][m [32m[main/INFO][m [36m(FabricLoader/MixinExtras|Service)[m [0mInitializing MixinExtras via com.llamalad7.mixinextras.service.MixinExtrasServiceImpl(version=0.4.1).
[m[34m[05:10:36][m [32m[Datafixer Bootstrap/INFO][m [36m(Minecraft)[m [0m188 Datafixer optimizations took 124 milliseconds
[m[34m[05:10:38][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mEnvironment: authHost='https://authserver.mojang.com', accountsHost='https://api.mojang.com', sessionHost='https://sessionserver.mojang.com', servicesHost='https://api.minecraftservices.com', name='PROD'
[m[34m[05:10:38][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mSetting user: Player689
[m[34m[05:10:38][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitializing PokeCobbleClaim mod
[m[34m[05:10:38][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering client-side network handlers
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering ConfigSynchronizer client-side packet handlers
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistered CONFIG_SYNC handler: pokecobbleclaim:config_sync
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistered USER_PREFERENCES_SYNC handler: pokecobbleclaim:user_preferences_sync
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering server-side network handlers
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering ConfigSynchronizer server-side packet handlers
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistered CONFIG_REQUEST handler: pokecobbleclaim:config_request
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistered CONFIG_UPDATE handler: pokecobbleclaim:config_update
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mReset all town and player data versions
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mFound 5 individual town files, loading...
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Mayor for player 25dc5eb7-e593-3192-87a1-eb20288493eb in town aaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player fb4c6c18-26a1-3d95-a13f-9a47871171a2 in town aaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 99e80a89-582d-3fb3-8388-49270da696a6 in town aaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 651bbe0e-980e-38d7-8fbb-83ebfec85347 in town aaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 6a350d2f-62d7-3aa3-951b-7fe208d8b2cb in town aaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1e8814df-4b51-3d88-9dcd-89145d90282f in town aaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 83f835f2-20fa-325d-8039-400fcc96db8c in town aaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player e7c5c79a-91ee-3db1-afe8-a6ff4d4f15fa in town aaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Mayor for player 25dc5eb7-e593-3192-87a1-eb20288493eb in town aaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player e7c5c79a-91ee-3db1-afe8-a6ff4d4f15fa in town aaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1e8814df-4b51-3d88-9dcd-89145d90282f in town aaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 6a350d2f-62d7-3aa3-951b-7fe208d8b2cb in town aaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Mayor for player 25dc5eb7-e593-3192-87a1-eb20288493eb in town aaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 99e80a89-582d-3fb3-8388-49270da696a6 in town aaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 651bbe0e-980e-38d7-8fbb-83ebfec85347 in town aaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 83f835f2-20fa-325d-8039-400fcc96db8c in town aaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 0 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player fb4c6c18-26a1-3d95-a13f-9a47871171a2 in town aaaaaaaaaa
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town d42f4ef4-ff82-4347-a77c-d322040ff0b2 - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town d42f4ef4-ff82-4347-a77c-d322040ff0b2 - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town d42f4ef4-ff82-4347-a77c-d322040ff0b2 - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town d42f4ef4-ff82-4347-a77c-d322040ff0b2 - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town d42f4ef4-ff82-4347-a77c-d322040ff0b2 - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town d42f4ef4-ff82-4347-a77c-d322040ff0b2 - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved settings to disk for town d42f4ef4-ff82-4347-a77c-d322040ff0b2
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet 6 town settings for town d42f4ef4-ff82-4347-a77c-d322040ff0b2
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRestored town settings for town aaaaaaaaaa: {isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying persisted settings to town aaaaaaaaaa (ID: d42f4ef4-ff82-4347-a77c-d322040ff0b2)
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSettings in memory: 6 entries
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoaded settings for town aaaaaaaaaa: {isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTown aaaaaaaaaa current image before applying settings: default
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting isOpen = true to town aaaaaaaaaa
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town d42f4ef4-ff82-4347-a77c-d322040ff0b2 - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting allowPublicBuilding = false to town aaaaaaaaaa
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town d42f4ef4-ff82-4347-a77c-d322040ff0b2 - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting showTownInList = true to town aaaaaaaaaa
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town d42f4ef4-ff82-4347-a77c-d322040ff0b2 - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting enablePvP = false to town aaaaaaaaaa
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town d42f4ef4-ff82-4347-a77c-d322040ff0b2 - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting enableTownChat = true to town aaaaaaaaaa
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town d42f4ef4-ff82-4347-a77c-d322040ff0b2 - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting allowMobSpawning = true to town aaaaaaaaaa
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town d42f4ef4-ff82-4347-a77c-d322040ff0b2 - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTown aaaaaaaaaa final image after applying settings: default
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplied 6 persisted settings to town aaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoaded town 'aaaaaaaaaa' with 8 players from d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player b40915cd-d3a7-3e3d-9a52-6fc5334ba2e3 in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2a040a11-2e73-3076-950d-54036bd62bd7 in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player f72636fb-c7db-3c44-8cd4-8a6790fd9b69 in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 9ef817a1-6950-35d1-b5a8-3ec720c28c3d in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player eb08048d-a3b9-3008-984c-fcc8bb7d8893 in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player e56ec8e1-c640-36ab-8281-ae4d44216993 in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1de0626e-6c80-37c2-856b-2a8735302b74 in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 5bd62aa7-8886-32aa-953e-97bdbc2a6931 in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 8e13e2e6-c022-3506-be47-7e24287c9023 in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player e4e5b30f-b972-382c-b5e5-86c2e785a8a6 in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 34077235-b312-3a6b-9fdd-0d5e06f91c0e in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2ecec10b-3fca-3e44-9b51-4cca88e1c207 in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 34692304-57b2-31f8-a84c-d22c6ff753f7 in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player d3ee42bb-6490-3bf6-992c-01b42b8a0039 in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1c207d49-b6d4-301f-bf62-f591002e4f1a in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player b0121b71-1aec-310d-8ee8-525ebe3fec09 in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 80db04bb-566c-3ad4-b0e2-8e4330dc410b in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player bce7419c-5709-3ee4-a73f-d19725fe2893 in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player b136c8aa-0654-3583-8db1-8749e12181a1 in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Mayor for player b40915cd-d3a7-3e3d-9a52-6fc5334ba2e3 in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 5bd62aa7-8886-32aa-953e-97bdbc2a6931 in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1de0626e-6c80-37c2-856b-2a8735302b74 in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1c207d49-b6d4-301f-bf62-f591002e4f1a in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 34077235-b312-3a6b-9fdd-0d5e06f91c0e in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player b40915cd-d3a7-3e3d-9a52-6fc5334ba2e3 in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2a040a11-2e73-3076-950d-54036bd62bd7 in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 34692304-57b2-31f8-a84c-d22c6ff753f7 in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player b136c8aa-0654-3583-8db1-8749e12181a1 in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 9ef817a1-6950-35d1-b5a8-3ec720c28c3d in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2ecec10b-3fca-3e44-9b51-4cca88e1c207 in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 80db04bb-566c-3ad4-b0e2-8e4330dc410b in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player e4e5b30f-b972-382c-b5e5-86c2e785a8a6 in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player e56ec8e1-c640-36ab-8281-ae4d44216993 in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player b0121b71-1aec-310d-8ee8-525ebe3fec09 in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player d3ee42bb-6490-3bf6-992c-01b42b8a0039 in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player eb08048d-a3b9-3008-984c-fcc8bb7d8893 in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 8e13e2e6-c022-3506-be47-7e24287c9023 in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player f72636fb-c7db-3c44-8cd4-8a6790fd9b69 in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 1 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player bce7419c-5709-3ee4-a73f-d19725fe2893 in town aaaaaaaaaaaaa
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56 - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56 - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56 - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56 - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56 - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56 - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved settings to disk for town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet 6 town settings for town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRestored town settings for town aaaaaaaaaaaaa: {isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying persisted settings to town aaaaaaaaaaaaa (ID: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56)
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSettings in memory: 6 entries
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoaded settings for town aaaaaaaaaaaaa: {isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTown aaaaaaaaaaaaa current image before applying settings: default
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting isOpen = true to town aaaaaaaaaaaaa
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56 - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting allowPublicBuilding = false to town aaaaaaaaaaaaa
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56 - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting showTownInList = true to town aaaaaaaaaaaaa
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56 - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting enablePvP = false to town aaaaaaaaaaaaa
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56 - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting enableTownChat = true to town aaaaaaaaaaaaa
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56 - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting allowMobSpawning = true to town aaaaaaaaaaaaa
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56 - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTown aaaaaaaaaaaaa final image after applying settings: default
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplied 6 persisted settings to town aaaaaaaaaaaaa
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoaded town 'aaaaaaaaaaaaa' with 19 players from 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved settings to disk for town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet 6 town settings for town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRestored town settings for town TestSettingsPersistence: {isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying persisted settings to town TestSettingsPersistence (ID: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b)
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSettings in memory: 6 entries
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoaded settings for town TestSettingsPersistence: {isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTown TestSettingsPersistence current image before applying settings: default
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting isOpen = false to town TestSettingsPersistence
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting allowPublicBuilding = false to town TestSettingsPersistence
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting showTownInList = true to town TestSettingsPersistence
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting enablePvP = false to town TestSettingsPersistence
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting enableTownChat = true to town TestSettingsPersistence
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting allowMobSpawning = true to town TestSettingsPersistence
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTown TestSettingsPersistence final image after applying settings: default
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplied 6 persisted settings to town TestSettingsPersistence
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoaded town 'TestSettingsPersistence' with 0 players from 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7 - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7 - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7 - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7 - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7 - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7 - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved settings to disk for town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet 6 town settings for town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRestored town settings for town TestSettingsPersistence: {isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying persisted settings to town TestSettingsPersistence (ID: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7)
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSettings in memory: 6 entries
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoaded settings for town TestSettingsPersistence: {isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTown TestSettingsPersistence current image before applying settings: default
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting isOpen = false to town TestSettingsPersistence
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7 - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting allowPublicBuilding = false to town TestSettingsPersistence
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7 - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting showTownInList = true to town TestSettingsPersistence
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7 - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting enablePvP = false to town TestSettingsPersistence
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7 - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting enableTownChat = true to town TestSettingsPersistence
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7 - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting allowMobSpawning = true to town TestSettingsPersistence
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7 - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTown TestSettingsPersistence final image after applying settings: default
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplied 6 persisted settings to town TestSettingsPersistence
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoaded town 'TestSettingsPersistence' with 0 players from 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Mayor for player c97b21a8-25e6-37ce-8a04-8d5bcf6f4c4d in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player fe207da4-45aa-3a2a-bf67-72a700290388 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 64579c2e-a410-37b0-995d-5aa98a8b0304 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player b2d19156-5364-397f-8e33-abd769d6319d in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2ef1cb8d-614b-3e2e-9366-5d5cf65cf8bf in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 0aee4775-e8b4-37ed-8049-6be96ef5de8e in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player b6529468-5313-3ea4-bc60-3e6ea6cabccd in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player a240c974-e4f5-311c-82e7-f92bb39b2584 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 492dc575-b72e-3d83-b2fd-33ab63727150 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player c2506432-c159-30c0-93d8-64f2a6272277 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player a0a7ec06-d96a-3187-943b-79bf7e87b3a2 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2737bbb4-080a-3670-89c1-780a32c86c5a in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 4941828d-27d9-3ea5-a0e2-8e881702c195 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player a205b8da-efc6-37ad-8e1d-84c0239cdd21 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player d944aee7-2684-33ce-b4ec-6dde00bdf73f in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1ab04e7d-c966-3477-8a64-78d9a4421f72 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2afb4ee9-7113-3f92-8544-46d36df86129 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 99963b1e-252d-3ba6-8126-9071dd762e6c in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 56a259bf-cc44-38c1-8061-dd05953c60e0 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player f2937d48-a72f-3375-bb6f-69c5f204d185 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 7d246c97-157b-31d3-a8fc-0b0f5f437b63 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 19487181-3859-391a-bf75-143cc8396d12 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1dd34f6b-f553-3906-92e2-e13f78ae2b51 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 33e9440c-90db-3f3e-bf40-dab751114d4d in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1acd01f1-1c8b-3758-959b-fdf4d16bd5de in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player fe412f9e-f508-30d0-b407-ff978cf8fc5c in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 77058a27-9ed9-3577-ba3a-f426b243548e in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 905847c0-6737-32d8-85e6-3f64ecd32033 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 5f452b45-a444-38e5-8393-5edeaa648813 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 043cbf82-02a9-3180-968e-af73411af080 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 3e1fbeae-13f8-3fbb-9842-63bc739da918 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 428a9eb8-eb59-3300-892c-1124ca047d38 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 3dc994a6-e4ba-3e8a-9bf9-4271ccca9810 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 9a9311e6-0e2a-3db9-849c-c74b23116910 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 8c6477df-589c-387e-8235-e07170c95726 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 324bf773-41c1-3c88-8191-03f2982049b1 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 198eff5d-ccf5-3ba5-aac5-eea0074f959b in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Mayor for player c97b21a8-25e6-37ce-8a04-8d5bcf6f4c4d in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2737bbb4-080a-3670-89c1-780a32c86c5a in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 324bf773-41c1-3c88-8191-03f2982049b1 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2ef1cb8d-614b-3e2e-9366-5d5cf65cf8bf in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 56a259bf-cc44-38c1-8061-dd05953c60e0 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 33e9440c-90db-3f3e-bf40-dab751114d4d in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 5f452b45-a444-38e5-8393-5edeaa648813 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 77058a27-9ed9-3577-ba3a-f426b243548e in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 9a9311e6-0e2a-3db9-849c-c74b23116910 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 3dc994a6-e4ba-3e8a-9bf9-4271ccca9810 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 19487181-3859-391a-bf75-143cc8396d12 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 8c6477df-589c-387e-8235-e07170c95726 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 2afb4ee9-7113-3f92-8544-46d36df86129 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 428a9eb8-eb59-3300-892c-1124ca047d38 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 0aee4775-e8b4-37ed-8049-6be96ef5de8e in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player a0a7ec06-d96a-3187-943b-79bf7e87b3a2 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player fe207da4-45aa-3a2a-bf67-72a700290388 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 492dc575-b72e-3d83-b2fd-33ab63727150 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player c2506432-c159-30c0-93d8-64f2a6272277 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player d944aee7-2684-33ce-b4ec-6dde00bdf73f in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player f2937d48-a72f-3375-bb6f-69c5f204d185 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 99963b1e-252d-3ba6-8126-9071dd762e6c in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 7d246c97-157b-31d3-a8fc-0b0f5f437b63 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1dd34f6b-f553-3906-92e2-e13f78ae2b51 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 3e1fbeae-13f8-3fbb-9842-63bc739da918 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Mayor for player c97b21a8-25e6-37ce-8a04-8d5bcf6f4c4d in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 64579c2e-a410-37b0-995d-5aa98a8b0304 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player a240c974-e4f5-311c-82e7-f92bb39b2584 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player b6529468-5313-3ea4-bc60-3e6ea6cabccd in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player a205b8da-efc6-37ad-8e1d-84c0239cdd21 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 198eff5d-ccf5-3ba5-aac5-eea0074f959b in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1acd01f1-1c8b-3758-959b-fdf4d16bd5de in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player fe412f9e-f508-30d0-b407-ff978cf8fc5c in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 1ab04e7d-c966-3477-8a64-78d9a4421f72 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 4941828d-27d9-3ea5-a0e2-8e881702c195 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 043cbf82-02a9-3180-968e-af73411af080 in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player b2d19156-5364-397f-8e33-abd769d6319d in town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaaaaa' to individual file: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'aaaaaaaaaa' to individual file: d42f4ef4-ff82-4347-a77c-d322040ff0b2.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved town 'TestSettingsPersistence' to individual file: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved 4 towns to individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet rank Resident for player 905847c0-6737-32d8-85e6-3f64ecd32033 in town test
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSaved settings to disk for town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSet 16 town settings for town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRestored town settings for town test: {image=test_test1, maxPlayers=100.0, d42f4ef4-ff82-4347-a77c-d322040ff0b2={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, 03e91d98-f031-4f21-b5f8-bb413f2cbf1b={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, showTownInList=true, 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, c279a2aa-6379-4f5a-9ec5-bce65fa5d52a={maxPlayers=100.0, d42f4ef4-ff82-4347-a77c-d322040ff0b2={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, 03e91d98-f031-4f21-b5f8-bb413f2cbf1b={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, showTownInList=true, 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, c279a2aa-6379-4f5a-9ec5-bce65fa5d52a={maxPlayers=100.0, d42f4ef4-ff82-4347-a77c-d322040ff0b2={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, 03e91d98-f031-4f21-b5f8-bb413f2cbf1b={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, showTownInList=true, 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, c279a2aa-6379-4f5a-9ec5-bce65fa5d52a={maxPlayers=100.0, d42f4ef4-ff82-4347-a77c-d322040ff0b2={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, 03e91d98-f031-4f21-b5f8-bb413f2cbf1b={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, showTownInList=true, 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, c279a2aa-6379-4f5a-9ec5-bce65fa5d52a={maxPlayers=100.0, isOpen=true, showTownInList=true, enableTownChat=true, name=test, description=yeeeee, allowPublicBuilding=false, enablePvP=false, allowMobSpawning=true}, description=yeeeee, isOpen=true, enableTownChat=true, name=test, 6a557cc3-fb5f-427a-a926-bafe2d3a0a56={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, allowPublicBuilding=false, enablePvP=false, allowMobSpawning=true}, description=yeeeee, isOpen=true, enableTownChat=true, name=test, 6a557cc3-fb5f-427a-a926-bafe2d3a0a56={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, allowPublicBuilding=false, enablePvP=false, allowMobSpawning=true}, description=yeeeee, isOpen=true, enableTownChat=true, name=test, 6a557cc3-fb5f-427a-a926-bafe2d3a0a56={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, allowPublicBuilding=false, enablePvP=false, allowMobSpawning=true}, description=yeeee, imageSettings={offsetX=0.0, offsetY=0.0, scale=1.0}, isOpen=true, enableTownChat=true, name=test, 6a557cc3-fb5f-427a-a926-bafe2d3a0a56={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, allowPublicBuilding=false, enablePvP=false, allowMobSpawning=true}
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying persisted settings to town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a)
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSettings in memory: 16 entries
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoaded settings for town test: {image=test_test1, maxPlayers=100.0, d42f4ef4-ff82-4347-a77c-d322040ff0b2={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, 03e91d98-f031-4f21-b5f8-bb413f2cbf1b={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, showTownInList=true, 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, c279a2aa-6379-4f5a-9ec5-bce65fa5d52a={maxPlayers=100.0, d42f4ef4-ff82-4347-a77c-d322040ff0b2={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, 03e91d98-f031-4f21-b5f8-bb413f2cbf1b={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, showTownInList=true, 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, c279a2aa-6379-4f5a-9ec5-bce65fa5d52a={maxPlayers=100.0, d42f4ef4-ff82-4347-a77c-d322040ff0b2={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, 03e91d98-f031-4f21-b5f8-bb413f2cbf1b={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, showTownInList=true, 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, c279a2aa-6379-4f5a-9ec5-bce65fa5d52a={maxPlayers=100.0, d42f4ef4-ff82-4347-a77c-d322040ff0b2={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, 03e91d98-f031-4f21-b5f8-bb413f2cbf1b={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, showTownInList=true, 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, c279a2aa-6379-4f5a-9ec5-bce65fa5d52a={maxPlayers=100.0, isOpen=true, showTownInList=true, enableTownChat=true, name=test, description=yeeeee, allowPublicBuilding=false, enablePvP=false, allowMobSpawning=true}, description=yeeeee, isOpen=true, enableTownChat=true, name=test, 6a557cc3-fb5f-427a-a926-bafe2d3a0a56={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, allowPublicBuilding=false, enablePvP=false, allowMobSpawning=true}, description=yeeeee, isOpen=true, enableTownChat=true, name=test, 6a557cc3-fb5f-427a-a926-bafe2d3a0a56={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, allowPublicBuilding=false, enablePvP=false, allowMobSpawning=true}, description=yeeeee, isOpen=true, enableTownChat=true, name=test, 6a557cc3-fb5f-427a-a926-bafe2d3a0a56={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, allowPublicBuilding=false, enablePvP=false, allowMobSpawning=true}, description=yeeee, imageSettings={offsetX=0.0, offsetY=0.0, scale=1.0}, isOpen=true, enableTownChat=true, name=test, 6a557cc3-fb5f-427a-a926-bafe2d3a0a56={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, allowPublicBuilding=false, enablePvP=false, allowMobSpawning=true}
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTown test current image before applying settings: test_test1
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting image = test_test1 to town test
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting maxPlayers = 100.0 to town test
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting d42f4ef4-ff82-4347-a77c-d322040ff0b2 = {isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true} to town test
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting 03e91d98-f031-4f21-b5f8-bb413f2cbf1b = {isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true} to town test
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting showTownInList = true to town test
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7 = {isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true} to town test
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting c279a2aa-6379-4f5a-9ec5-bce65fa5d52a = {maxPlayers=100.0, d42f4ef4-ff82-4347-a77c-d322040ff0b2={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, 03e91d98-f031-4f21-b5f8-bb413f2cbf1b={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, showTownInList=true, 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, c279a2aa-6379-4f5a-9ec5-bce65fa5d52a={maxPlayers=100.0, d42f4ef4-ff82-4347-a77c-d322040ff0b2={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, 03e91d98-f031-4f21-b5f8-bb413f2cbf1b={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, showTownInList=true, 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, c279a2aa-6379-4f5a-9ec5-bce65fa5d52a={maxPlayers=100.0, d42f4ef4-ff82-4347-a77c-d322040ff0b2={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, 03e91d98-f031-4f21-b5f8-bb413f2cbf1b={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, showTownInList=true, 1de9d0d8-0433-4cc8-9e0e-31e2ca16c1b7={isOpen=false, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, c279a2aa-6379-4f5a-9ec5-bce65fa5d52a={maxPlayers=100.0, isOpen=true, showTownInList=true, enableTownChat=true, name=test, description=yeeeee, allowPublicBuilding=false, enablePvP=false, allowMobSpawning=true}, description=yeeeee, isOpen=true, enableTownChat=true, name=test, 6a557cc3-fb5f-427a-a926-bafe2d3a0a56={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, allowPublicBuilding=false, enablePvP=false, allowMobSpawning=true}, description=yeeeee, isOpen=true, enableTownChat=true, name=test, 6a557cc3-fb5f-427a-a926-bafe2d3a0a56={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, allowPublicBuilding=false, enablePvP=false, allowMobSpawning=true}, description=yeeeee, isOpen=true, enableTownChat=true, name=test, 6a557cc3-fb5f-427a-a926-bafe2d3a0a56={isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true}, allowPublicBuilding=false, enablePvP=false, allowMobSpawning=true} to town test
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting description = yeeee to town test
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting imageSettings = {offsetX=0.0, offsetY=0.0, scale=1.0} to town test
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting isOpen = true to town test
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting enableTownChat = true to town test
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting name = test to town test
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting 6a557cc3-fb5f-427a-a926-bafe2d3a0a56 = {isOpen=true, allowPublicBuilding=false, showTownInList=true, enablePvP=false, enableTownChat=true, allowMobSpawning=true} to town test
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting allowPublicBuilding = false to town test
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting enablePvP = false to town test
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplying setting allowMobSpawning = true to town test
[m[34m[05:10:39][m [33m[Render thread/WARN][m [36m(pokecobbleclaim)[m [0mCannot apply setting to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a - town not found in TownManager or ClientTownManager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTown test final image after applying settings: test_test1
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mApplied 16 persisted settings to town test
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoaded town 'test' with 37 players from c279a2aa-6379-4f5a-9ec5-bce65fa5d52a.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRestored 64 player-town relationships
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoaded 5 towns from individual files
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTown data loading completed. Final town count in TownManager: 5
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mPokeCobbleClaim mod initialized successfully
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitializing PokeCobbleClaim client
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering town keybinding
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering chunk boundary renderer
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoaded user preferences from: pokecobbleclaim-user-preferences.json
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitialized client-side data managers for synchronization
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering sounds
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSuccessfully registered sound: pokecobbleclaim:notification.invite
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSuccessfully registered sound: pokecobbleclaim:ui.button.click
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistered sounds on client side
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitializing phone feature
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitializing app position manager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitializing app position manager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSetting up default app positions
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitialized 24 default app positions
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitializing app registry
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitializing app registry
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistered 5 apps
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitializing phone texture manager
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitializing and registering phone notification overlay
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering phone notification overlay renderer
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering phone notification renderer
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistered phone notification renderer
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering phone keybinding
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering phone keybinding
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitialized phone feature
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering Shape Visualizer Tool
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistered shape visualizer tool
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mPokeCobbleClaim client initialized successfully
[m[34m[05:10:39][m [32m[Render thread/INFO][m [36m(Indigo)[m [0m[Indigo] Registering Indigo renderer!
[m[34m[05:10:40][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mBackend library: LWJGL version 3.3.2-snapshot
[m[34m[05:10:40][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0m[STDERR]: [LWJGL] [ThreadLocalUtil] Unsupported JNI version detected, this may result in a crash. Please inform LWJGL developers.
[m[34m[05:10:41][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mReloading ResourceManager: vanilla, fabric (fabric-block-api-v1, fabric-content-registries-v0, fabric-loot-tables-v1, pokecobbleclaim, fabric-message-api-v1, fabric-renderer-indigo, fabric-rendering-data-attachment-v1, fabric-screen-handler-api-v1, fabric-registry-sync-v0, fabric-entity-events-v1, fabric-renderer-api-v1, fabric-transfer-api-v1, fabric-key-binding-api-v1, fabric-crash-report-info-v1, fabric-transitive-access-wideners-v1, fabric-recipe-api-v1, fabric-gametest-api-v1, fabric-particles-v1, fabric-screen-api-v1, fabric-object-builder-api-v1, fabric-containers-v0, fabric-loot-api-v2, fabric-lifecycle-events-v1, fabric-commands-v0, fabric-resource-loader-v0, fabric-blockrenderlayer-v1, fabric-command-api-v1, fabric-events-interaction-v0, fabric-item-group-api-v1, fabric-biome-api-v1, fabricloader, fabric-events-lifecycle-v0, fabric-mining-level-api-v1, fabric-rendering-v0, fabric-resource-conditions-api-v1, fabric-data-attachment-api-v1, fabric-sound-api-v1, fabric-dimensions-v1, fabric-data-generation-api-v1, fabric-keybindings-v0, fabric-networking-api-v1, fabric-rendering-v1, fabric-rendering-fluids-v1, fabric-api-base, fabric-models-v0, fabric-item-api-v1, fabric-client-tags-api-v1, fabric-command-api-v2, fabric-model-loading-api-v1, fabric-api-lookup-api-v1, fabric-api, fabric-game-rule-api-v1, fabric-convention-tags-v1, fabric-networking-v0, fabric-block-view-api-v2, fabric-renderer-registries-v1)
[m[34m[05:10:41][m [32m[Worker-Main-1/INFO][m [36m(Minecraft)[m [0mFound unifont_all_no_pua-15.0.06.hex, loading
[m[34m[05:10:42][m [32m[Realms Notification Availability checker #1/INFO][m [36m(Minecraft)[m [0mCould not authorize you against Realms server: java.lang.RuntimeException: Failed to parse into SignedJWT: FabricMC
[m[34m[05:10:43][m [33m[Render thread/WARN][m [36m(Minecraft)[m [0mMissing sound for event: minecraft:item.goat_horn.play
[m[34m[05:10:43][m [33m[Render thread/WARN][m [36m(Minecraft)[m [0mMissing sound for event: minecraft:entity.goat.screaming.horn_break
[m[34m[05:10:43][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mOpenAL initialized on device Sound Blaster GC7 Analog Stereo
[m[34m[05:10:43][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mSound engine started
[m[34m[05:10:43][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mCreated: 1024x512x4 minecraft:textures/atlas/blocks.png-atlas
[m[34m[05:10:43][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mCreated: 256x256x4 minecraft:textures/atlas/signs.png-atlas
[m[34m[05:10:43][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mCreated: 512x512x4 minecraft:textures/atlas/banner_patterns.png-atlas
[m[34m[05:10:43][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mCreated: 512x512x4 minecraft:textures/atlas/shield_patterns.png-atlas
[m[34m[05:10:43][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mCreated: 1024x1024x4 minecraft:textures/atlas/armor_trims.png-atlas
[m[34m[05:10:43][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mCreated: 128x64x4 minecraft:textures/atlas/decorated_pot.png-atlas
[m[34m[05:10:43][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mCreated: 256x256x4 minecraft:textures/atlas/chest.png-atlas
[m[34m[05:10:43][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mCreated: 512x256x4 minecraft:textures/atlas/shulker_boxes.png-atlas
[m[34m[05:10:43][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mCreated: 512x256x4 minecraft:textures/atlas/beds.png-atlas
[m[34m[05:10:44][m [33m[Render thread/WARN][m [36m(Minecraft)[m [0mShader rendertype_entity_translucent_emissive could not find sampler named Sampler2 in the specified shader program.
[m[34m[05:10:44][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mCreated: 256x256x0 minecraft:textures/atlas/particles.png-atlas
[m[34m[05:10:44][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mCreated: 256x256x0 minecraft:textures/atlas/paintings.png-atlas
[m[34m[05:10:44][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mCreated: 128x128x0 minecraft:textures/atlas/mob_effects.png-atlas
[m[34m[05:10:47][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mConnecting to localhost, 25565
[m[34m[05:10:48][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mReceived town list response with 5 towns
[m[34m[05:10:48][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mReceived towns: aaaaaaaaaaaaa, TestSettingsPersistence, test, aaaaaaaaaa, TestSettingsPersistence, 
[m[34m[05:10:48][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mUpdated town list cache (version 1, 5 towns)
[m[34m[05:10:48][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mTowns in cache: aaaaaaaaaaaaa, TestSettingsPersistence, test, aaaaaaaaaa, TestSettingsPersistence, 
[m[34m[05:10:48][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mReceived town image update for aaaaaaaaaaaaa: default (data version: 3)
[m[34m[05:10:48][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mReceived town image update for TestSettingsPersistence: default (data version: 3)
[m[34m[05:10:48][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mReceived town image update for test: test_test1 (data version: 3)
[m[34m[05:10:48][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mReceived town image update for aaaaaaaaaa: default (data version: 3)
[m[34m[05:10:48][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mReceived town image update for TestSettingsPersistence: default (data version: 3)
[m[34m[05:10:48][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading positions for player: e7f0f282-ec78-3bba-aaaf-9edc548f540d
[m[34m[05:10:48][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mPositions file doesn't exist, using default positions
[m[34m[05:10:48][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitialized with default positions
[m[34m[05:10:48][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering client-side town commands
[m[34m[05:10:48][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClient-side town commands registered successfully
[m[34m[05:10:49][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mLoaded 2 advancements
[m[34m[05:10:51][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTown key pressed, opening town screen
[m[34m[05:10:51][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mOpening town screen
[m[34m[05:10:51][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Attempting to send packet to server - ID: pokecobbleclaim:town_data_request, Size: 16 bytes
[m[34m[05:10:51][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Packet sent successfully to server - ID: pokecobbleclaim:town_data_request
[m[34m[05:10:51][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Attempting to send packet to server - ID: pokecobbleclaim:town_list_request, Size: 16 bytes
[m[34m[05:10:51][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Packet sent successfully to server - ID: pokecobbleclaim:town_list_request
[m[34m[05:10:51][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mReceived town list response with 5 towns
[m[34m[05:10:51][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mReceived towns: aaaaaaaaaaaaa, TestSettingsPersistence, test, aaaaaaaaaa, TestSettingsPersistence, 
[m[34m[05:10:52][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSending town join request for player Player689 to town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a
[m[34m[05:10:52][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Attempting to send packet to server - ID: pokecobbleclaim:town_join_request, Size: 32 bytes
[m[34m[05:10:52][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Packet sent successfully to server - ID: pokecobbleclaim:town_join_request
[m[34m[05:10:52][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTown join request packet sent successfully
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Attempting to send packet to server - ID: pokecobbleclaim:town_data_request, Size: 16 bytes
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Packet sent successfully to server - ID: pokecobbleclaim:town_data_request
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mReceived town list response with 5 towns
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mReceived towns: aaaaaaaaaaaaa, TestSettingsPersistence, test, aaaaaaaaaa, TestSettingsPersistence, 
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Attempting to send packet to server - ID: pokecobbleclaim:town_data_request, Size: 16 bytes
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Packet sent successfully to server - ID: pokecobbleclaim:town_data_request
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mReceived town list response with 5 towns
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mReceived towns: aaaaaaaaaaaaa, TestSettingsPersistence, test, aaaaaaaaaa, TestSettingsPersistence, 
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mReceived town join response: success=true, message=Successfully joined test
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mUpdated client player town association: player Player689 joined town test (c279a2aa-6379-4f5a-9ec5-bce65fa5d52a)
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mTown join successful - server will sync membership data automatically
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Attempting to send packet to server - ID: pokecobbleclaim:town_data_request, Size: 16 bytes
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Packet sent successfully to server - ID: pokecobbleclaim:town_data_request
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mSuccessfully joined town: Successfully joined test
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mReceived player town membership: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a (version 4)
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mReceived player town membership: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a (version 4)
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Attempting to send packet to server - ID: pokecobbleclaim:town_data_request, Size: 16 bytes
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Packet sent successfully to server - ID: pokecobbleclaim:town_data_request
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Attempting to send packet to server - ID: pokecobbleclaim:town_data_request, Size: 16 bytes
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Packet sent successfully to server - ID: pokecobbleclaim:town_data_request
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Attempting to send packet to server - ID: pokecobbleclaim:town_data_request, Size: 16 bytes
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Packet sent successfully to server - ID: pokecobbleclaim:town_data_request
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mReceived town image update for test: test_test1 (data version: 4)
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Attempting to send packet to server - ID: pokecobbleclaim:town_data_request, Size: 16 bytes
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Packet sent successfully to server - ID: pokecobbleclaim:town_data_request
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Attempting to send packet to server - ID: pokecobbleclaim:town_data_request, Size: 16 bytes
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Packet sent successfully to server - ID: pokecobbleclaim:town_data_request
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Attempting to send packet to server - ID: pokecobbleclaim:town_data_request, Size: 16 bytes
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Packet sent successfully to server - ID: pokecobbleclaim:town_data_request
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mReceived town image update for aaaaaaaaaaaaa: default (data version: 3)
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Attempting to send packet to server - ID: pokecobbleclaim:town_data_request, Size: 16 bytes
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Packet sent successfully to server - ID: pokecobbleclaim:town_data_request
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Attempting to send packet to server - ID: pokecobbleclaim:town_data_request, Size: 16 bytes
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Packet sent successfully to server - ID: pokecobbleclaim:town_data_request
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Attempting to send packet to server - ID: pokecobbleclaim:town_data_request, Size: 16 bytes
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Packet sent successfully to server - ID: pokecobbleclaim:town_data_request
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mReceived town image update for TestSettingsPersistence: default (data version: 3)
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Attempting to send packet to server - ID: pokecobbleclaim:town_data_request, Size: 16 bytes
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Packet sent successfully to server - ID: pokecobbleclaim:town_data_request
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Attempting to send packet to server - ID: pokecobbleclaim:town_data_request, Size: 16 bytes
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Packet sent successfully to server - ID: pokecobbleclaim:town_data_request
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Attempting to send packet to server - ID: pokecobbleclaim:town_data_request, Size: 16 bytes
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Packet sent successfully to server - ID: pokecobbleclaim:town_data_request
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mReceived town image update for aaaaaaaaaa: default (data version: 3)
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Attempting to send packet to server - ID: pokecobbleclaim:town_data_request, Size: 16 bytes
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Packet sent successfully to server - ID: pokecobbleclaim:town_data_request
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Attempting to send packet to server - ID: pokecobbleclaim:town_data_request, Size: 16 bytes
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Packet sent successfully to server - ID: pokecobbleclaim:town_data_request
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Attempting to send packet to server - ID: pokecobbleclaim:town_data_request, Size: 16 bytes
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Packet sent successfully to server - ID: pokecobbleclaim:town_data_request
[m[34m[05:10:52][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mReceived town image update for TestSettingsPersistence: default (data version: 3)
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mModernTownScreen.selectCategory: playerTownId=c279a2aa-6379-4f5a-9ec5-bce65fa5d52a, playerTown=test
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Attempting to send packet to server - ID: pokecobbleclaim:town_data_request, Size: 16 bytes
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Packet sent successfully to server - ID: pokecobbleclaim:town_data_request
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Attempting to send packet to server - ID: pokecobbleclaim:town_data_request, Size: 16 bytes
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Packet sent successfully to server - ID: pokecobbleclaim:town_data_request
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:53][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:54][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:55][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:55][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:55][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:55][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:55][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:55][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:55][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:55][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:55][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:55][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:55][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:55][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:55][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:55][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:55][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:55][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:10:55][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:10:55][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:10:55][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:10:55][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:10:55][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Attempting to send packet to server - ID: pokecobbleclaim:town_data_request, Size: 16 bytes
[m[34m[05:10:55][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Packet sent successfully to server - ID: pokecobbleclaim:town_data_request
[m[34m[05:10:55][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Attempting to send packet to server - ID: pokecobbleclaim:town_list_request, Size: 16 bytes
[m[34m[05:10:55][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Packet sent successfully to server - ID: pokecobbleclaim:town_list_request
[m[34m[05:10:55][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mReceived town list response with 5 towns
[m[34m[05:10:55][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mReceived towns: aaaaaaaaaaaaa, TestSettingsPersistence, test, aaaaaaaaaa, TestSettingsPersistence, 
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mModernTownScreen.selectCategory: playerTownId=c279a2aa-6379-4f5a-9ec5-bce65fa5d52a, playerTown=test
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Attempting to send packet to server - ID: pokecobbleclaim:town_data_request, Size: 16 bytes
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Packet sent successfully to server - ID: pokecobbleclaim:town_data_request
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Attempting to send packet to server - ID: pokecobbleclaim:town_data_request, Size: 16 bytes
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Packet sent successfully to server - ID: pokecobbleclaim:town_data_request
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Attempting to send packet to server - ID: pokecobbleclaim:town_data_request, Size: 16 bytes
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Packet sent successfully to server - ID: pokecobbleclaim:town_data_request
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:04][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:05][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Attempting to send packet to server - ID: pokecobbleclaim:town_data_request, Size: 16 bytes
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Packet sent successfully to server - ID: pokecobbleclaim:town_data_request
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Attempting to send packet to server - ID: pokecobbleclaim:town_list_request, Size: 16 bytes
[m[34m[05:11:06][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Packet sent successfully to server - ID: pokecobbleclaim:town_list_request
[m[34m[05:11:06][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mReceived town list response with 5 towns
[m[34m[05:11:06][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mReceived towns: aaaaaaaaaaaaa, TestSettingsPersistence, test, aaaaaaaaaa, TestSettingsPersistence, 
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mModernTownScreen.selectCategory: playerTownId=c279a2aa-6379-4f5a-9ec5-bce65fa5d52a, playerTown=test
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Attempting to send packet to server - ID: pokecobbleclaim:town_data_request, Size: 16 bytes
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Packet sent successfully to server - ID: pokecobbleclaim:town_data_request
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Attempting to send packet to server - ID: pokecobbleclaim:town_data_request, Size: 16 bytes
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Packet sent successfully to server - ID: pokecobbleclaim:town_data_request
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Attempting to send packet to server - ID: pokecobbleclaim:town_data_request, Size: 16 bytes
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mCLIENT: Packet sent successfully to server - ID: pokecobbleclaim:town_data_request
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:57][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mMyTownScreen: Town test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a) has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mWARNING: MyTownScreen town object is different from TownManager town object!
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClientTownManager town test has image: default
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoading default town image: default for town: test, result: not found
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClick at (159,81) - Circle at (145,80) with radius 25
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClick is inside the circle
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mOpening TownImageScreen
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mCreated circular mask texture
[m[34m[05:11:58][m [32m[Netty Epoll Client IO #0/INFO][m [36m(pokecobbleclaim)[m [0mReceived town image update for test: test_test1 (data version: 4)
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mAttempting to load test image: test1
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTrying resource: pokecobbleclaim:textures/townpicturetest/test1.png
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTrying resource: pokecobbleclaim:textures/townpicturetest/test1.jpg
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTrying resource: pokecobbleclaim:textures/townpicturetest/test1.jpeg
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTrying resource: pokecobbleclaim:townpicturetest/test1.png
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTrying resource: pokecobbleclaim:townpicturetest/test1.jpg
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSuccessfully found test image: pokecobbleclaim:townpicturetest/test1.jpg
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mAttempting to load test image: test2
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTrying resource: pokecobbleclaim:textures/townpicturetest/test2.png
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTrying resource: pokecobbleclaim:textures/townpicturetest/test2.jpg
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTrying resource: pokecobbleclaim:textures/townpicturetest/test2.jpeg
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTrying resource: pokecobbleclaim:townpicturetest/test2.png
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSuccessfully found test image: pokecobbleclaim:townpicturetest/test2.png
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mAttempting to load test image: test3
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTrying resource: pokecobbleclaim:textures/townpicturetest/test3.png
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTrying resource: pokecobbleclaim:textures/townpicturetest/test3.jpg
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTrying resource: pokecobbleclaim:textures/townpicturetest/test3.jpeg
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTrying resource: pokecobbleclaim:townpicturetest/test3.png
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTrying resource: pokecobbleclaim:townpicturetest/test3.jpg
[m[34m[05:11:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSuccessfully found test image: pokecobbleclaim:townpicturetest/test3.jpg
[m